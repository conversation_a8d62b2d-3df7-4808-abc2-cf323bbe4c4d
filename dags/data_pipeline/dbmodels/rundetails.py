import datetime

from sqlalchemy.orm import Mapped, mapped_column

from .base import Base, TableName
from sqlalchemy import Column, MetaData, Table, String, Integer, DateTime
from sqlalchemy.dialects.postgresql import TEXT, TIMESTAMP


class RunDetailsJira(Base):
    use_snake_case = True
    topic: Mapped[str] = mapped_column(TEXT, primary_key=True)
    last_run: Mapped[datetime.datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False)


metadata_obj = MetaData(schema=None)
PGStatActivity = Table(
    "pg_stat_activity",
    metadata_obj,
    Column('datname', String, primary_key=True),
    Column('usename', String, primary_key=True),
    Column('pid', Integer, primary_key=True),
    Column('state', String),
)