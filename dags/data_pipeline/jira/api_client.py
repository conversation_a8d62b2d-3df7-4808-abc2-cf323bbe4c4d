import asyncio
import math
import os
import random
import sys
import threading

from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from logging import Logger
import socket
from typing import Union, Dict, Any, Optional, List

import aiohttp
from dateutil import parser
from dependency_injector.wiring import inject, Provide

from dags.data_pipeline.containers import (
    Logger<PERSON>ontainer, CircuitBreakerContainer, )
from dags.data_pipeline.utils.circuit_breaker import SimplifiedCircuitBreaker, ErrorType
from dags.data_pipeline.utils.env_config import get_env_config
from dags.data_pipeline.debug.debug_utils import rate_limit_tracker

# Load JIRA configuration from environment variables
env_config = get_env_config()
jira_config = env_config.get_jira_config()

MAX_RETRIES = jira_config.max_retries
INITIAL_RETRY_DELAY = float(jira_config.initial_retry_delay)  # in milliseconds
MAX_RETRY_DELAY = jira_config.max_retry_delay  # in milliseconds
JITTER_MULTIPLIER_RANGE = (jira_config.jitter_min, jira_config.jitter_max)


@dataclass
class ConnectorMetrics:
    """Structured connector metrics for database storage"""
    # Session identification
    session_name: str
    session_id: str
    timestamp: datetime

    # Connection status
    session_closed: bool
    connector_closed: bool
    connector_type: str

    # Connection limits
    total_limit: int
    per_host_limit: int

    # Connection statistics
    total_acquired: int
    total_available: int
    active_hosts: int
    global_acquired_pool_size: int

    # Utilization metrics
    total_utilization_pct: float
    is_at_capacity: bool
    is_near_capacity: bool  # >80% utilization

    # Health indicators
    health_status: str  # 'healthy', 'warning', 'critical', 'idle'
    has_cleanup_task: bool
    cleanup_task_active: bool

    # Per-host details (for JSONB storage)
    host_details: List[Dict[str, Any]]

    # System context
    process_id: int
    thread_id: int
    hostname: str

    # Error information (if any)
    error_occurred: bool
    error_message: Optional[str] = None
    error_line: Optional[int] = None


class ConnectorLogger:
    """Handles both visual and database-friendly connector logging"""

    def __init__(self, logger: Optional[Logger] = None):
        self.logger = logger
        self.hostname = socket.gethostname()
        self.process_id = os.getpid()

    def log_connector_attrs(
            self,
            name: str,
            session: aiohttp.client.ClientSession,
            correlation_id: Optional[str] = None,
            task_id: Optional[str] = None,
            visual_output: bool = True,
            return_metrics: bool = True
    ) -> Optional[ConnectorMetrics]:
        """
        Log connector attributes with both visual and structured output.

        Args:
            name: Descriptive name for this session/context
            session: The aiohttp ClientSession to inspect
            correlation_id: Correlation ID for tracking related operations
            task_id: Task ID for async operations
            visual_output: Whether to produce visual console/file output
            return_metrics: Whether to return structured metrics for DB storage

        Returns:
            ConnectorMetrics object if return_metrics=True, else None
        """
        # Handle both actual logger and Provide objects

        for level in ("debug", "info", "warning", "error"):
            if callable(method := getattr(self.logger, level, None)):
                log = method
                break
        else:
            log = print


        # Initialize metrics
        metrics = None
        error_occurred = False
        error_message = None
        error_line = None

        try:
            # Get basic session info
            session_id = str(id(session))
            timestamp = datetime.now()

            conn = session._connector
            if not conn:
                if visual_output:
                    log(f"❌ No connector found for session: {name}")

                if return_metrics:
                    metrics = ConnectorMetrics(
                        session_name=name,
                        session_id=session_id,
                        timestamp=timestamp,
                        session_closed=session.closed,
                        connector_closed=True,
                        connector_type="None",
                        total_limit=0,
                        per_host_limit=0,
                        total_acquired=0,
                        total_available=0,
                        active_hosts=0,
                        global_acquired_pool_size=0,
                        total_utilization_pct=0.0,
                        is_at_capacity=False,
                        is_near_capacity=False,
                        health_status="no_connector",
                        has_cleanup_task=False,
                        cleanup_task_active=False,
                        host_details=[],
                        process_id=self.process_id,
                        thread_id=threading.get_ident() if hasattr(threading, 'get_ident') else 0,
                        hostname=self.hostname,
                        error_occurred=False
                    )
                return metrics

            # Collect connector data
            acquired_per_host = conn._acquired_per_host
            total_acquired = sum(len(connections) for connections in acquired_per_host.values())
            total_hosts = len(acquired_per_host)

            # Calculate utilization
            total_utilization_pct = (total_acquired / conn.limit * 100) if conn.limit > 0 else 0.0
            is_at_capacity = total_acquired >= conn.limit
            is_near_capacity = total_utilization_pct > 80.0

            # Determine health status
            if total_acquired == 0:
                health_status = "idle"
            elif is_at_capacity:
                health_status = "critical"
            elif is_near_capacity:
                health_status = "warning"
            else:
                health_status = "healthy"

            # Get cleanup task info
            has_cleanup_task = hasattr(conn, '_cleanup_handle') and conn._cleanup_handle is not None
            cleanup_task_active = has_cleanup_task and not conn._cleanup_handle.cancelled()

            # Collect per-host details
            host_details = []
            total_available = 0

            for key, transports in acquired_per_host.items():
                try:
                    available = conn._available_connections(key)
                    total_available += available
                    acquired_count = len(transports)

                    host_utilization_pct = 0.0
                    if conn.limit_per_host > 0:
                        host_utilization_pct = (acquired_count / conn.limit_per_host) * 100

                    host_detail = {
                        "host": key.host,
                        "port": key.port,
                        "acquired": acquired_count,
                        "available": available,
                        "total": acquired_count + available,
                        "utilization_pct": round(host_utilization_pct, 1),
                        "is_ssl": getattr(key, 'is_ssl', False),
                        "at_limit": acquired_count >= conn.limit_per_host if conn.limit_per_host > 0 else False
                    }
                    host_details.append(host_detail)

                except Exception as host_error:
                    # Log host-specific errors but continue
                    if visual_output:
                        log(f"Error processing host {key.host}:{key.port}: {host_error}")

            # Create metrics object
            if return_metrics:
                metrics = ConnectorMetrics(
                    session_name=name,
                    session_id=session_id,
                    timestamp=timestamp,
                    session_closed=session.closed,
                    connector_closed=conn._closed,
                    connector_type=type(conn).__name__,
                    total_limit=conn.limit,
                    per_host_limit=conn.limit_per_host,
                    total_acquired=total_acquired,
                    total_available=total_available,
                    active_hosts=total_hosts,
                    global_acquired_pool_size=len(conn._acquired),
                    total_utilization_pct=round(total_utilization_pct, 1),
                    is_at_capacity=is_at_capacity,
                    is_near_capacity=is_near_capacity,
                    health_status=health_status,
                    has_cleanup_task=has_cleanup_task,
                    cleanup_task_active=cleanup_task_active,
                    host_details=host_details,
                    process_id=self.process_id,
                    thread_id=threading.get_ident() if hasattr(threading, 'get_ident') else 0,
                    hostname=self.hostname,
                    error_occurred=False
                )

            # Visual output (if requested)
            if visual_output:
                if health_status in ("warning", "critical") or total_utilization_pct > 50.0:
                    self._log_visual_output(name, metrics, conn, host_details)

            # Log structured data to database
            if self.logger and return_metrics:
                if health_status in ("warning", "critical") or total_utilization_pct > 50.0:
                    self._log_structured_data(metrics, correlation_id, task_id)

        except Exception as e:
            error_occurred = True
            error_message = str(e)

            exc_type, exc_value, exc_tb = sys.exc_info()
            error_line = exc_tb.tb_lineno if exc_tb else None

            if visual_output:
                log(f"❌ Error in connector diagnostics for '{name}': {error_message}")
                if self.logger:
                    self.logger.exception(f"Connector diagnostics failed for session '{name}'")

            # Create error metrics
            if return_metrics:
                metrics = ConnectorMetrics(
                    session_name=name,
                    session_id=session_id if 'session_id' in locals() else "unknown",
                    timestamp=timestamp if 'timestamp' in locals() else datetime.now(),
                    session_closed=session.closed,
                    connector_closed=True,
                    connector_type="unknown",
                    total_limit=0,
                    per_host_limit=0,
                    total_acquired=0,
                    total_available=0,
                    active_hosts=0,
                    global_acquired_pool_size=0,
                    total_utilization_pct=0.0,
                    is_at_capacity=False,
                    is_near_capacity=False,
                    health_status="error",
                    has_cleanup_task=False,
                    cleanup_task_active=False,
                    host_details=[],
                    process_id=self.process_id,
                    thread_id=threading.get_ident() if hasattr(threading, 'get_ident') else 0,
                    hostname=self.hostname,
                    error_occurred=True,
                    error_message=error_message,
                    error_line=error_line
                )

        return metrics

    def _log_visual_output(self, name: str, metrics: ConnectorMetrics, conn, host_details: List[Dict]):
        """Generate visual console/file output"""
        log = self.logger.debug if self.logger else print

        log(f"\n{'=' * 60}")
        log(f"🔌 CONNECTION DIAGNOSTICS: {name}")
        log(f"{'=' * 60}")

        # Status section
        log(f"📊 STATUS:")
        log(f"   • Session: {'❌ CLOSED' if metrics.session_closed else '✅ OPEN'}")
        log(f"   • Connector: {'❌ CLOSED' if metrics.connector_closed else '✅ OPEN'}")
        log(f"   • Type: {metrics.connector_type}")

        # Limits section
        log(f"\n🎯 LIMITS:")
        log(f"   • Total: {metrics.total_limit}")
        log(f"   • Per-host: {metrics.per_host_limit}")

        # Statistics section
        log(f"\n📈 STATISTICS:")
        log(f"   • Acquired: {metrics.total_acquired}")
        log(f"   • Available: {metrics.total_available}")
        log(f"   • Active hosts: {metrics.active_hosts}")
        log(f"   • Utilization: {metrics.total_utilization_pct}%")

        # Health section
        status_emoji = {"healthy": "✅", "warning": "⚠️", "critical": "🔴", "idle": "ℹ️", "error": "❌"}
        log(f"\n🏥 HEALTH: {status_emoji.get(metrics.health_status, '❓')} {metrics.health_status.upper()}")

        # Host details
        if host_details:
            log(f"\n🌐 HOSTS:")
            for i, host in enumerate(host_details, 1):
                health = "🔴" if host["at_limit"] else "🟢"
                log(f"   {health} {host['host']}:{host['port']}")
                log(f"      ├─ Acquired: {host['acquired']} ({host['utilization_pct']}%)")
                log(f"      └─ Available: {host['available']}")

        log(f"{'=' * 60}")

    def _log_structured_data(self, metrics: ConnectorMetrics, correlation_id: Optional[str], task_id: Optional[str]):
        """Log structured data suitable for database storage"""
        if not self.logger:
            return

        # Create structured log entry
        extra_data = {
            "connector_metrics": asdict(metrics),
            "metric_type": "connector_diagnostics",
            "analysis_ready": True
        }

        # Performance data for separate column
        performance_data = {
            "total_utilization_pct": metrics.total_utilization_pct,
            "total_acquired": metrics.total_acquired,
            "total_available": metrics.total_available,
            "active_hosts": metrics.active_hosts,
            "is_at_capacity": metrics.is_at_capacity,
            "is_near_capacity": metrics.is_near_capacity,
            "health_status": metrics.health_status
        }

        # Log with structured data
        self.logger.info(
            f"Connector diagnostics for session '{metrics.session_name}': "
            f"{metrics.health_status} status, {metrics.total_utilization_pct}% utilization, "
            f"{metrics.total_acquired} acquired connections across {metrics.active_hosts} hosts",
            extra={
                # "correlation_id": correlation_id,
                # "task_id": task_id,
                "extra_data": extra_data,
                "performance_data": performance_data,
                "session_name": metrics.session_name,
                "session_id": metrics.session_id,
                "connector_type": metrics.connector_type,
                "health_status": metrics.health_status
            }
        )


@inject
async def fetch_with_retries(
        session: aiohttp.ClientSession,
        method: str,
        url: str,
        *,
        params: Union[Dict[str, Any], None] = None,
        json_payload: Union[Dict[str, Any], None] = None,
        retries: int = MAX_RETRIES,
        my_logger: Logger = Provide[LoggerContainer.logger],
        # global_circuit_breaker: GlobalCircuitBreaker = Provide[CircuitBreakerContainer.global_circuit_breaker],
        simplified_circuit_breaker: SimplifiedCircuitBreaker = Provide[CircuitBreakerContainer.simplified_circuit_breaker],

) -> Dict[str, Any]:
    """
    Perform an HTTP request with retry logic.

    :param session: aiohttp ClientSession.
    :param method: HTTP method (e.g., 'GET', 'POST', 'PUT').
    :param url: API endpoint URL.
    :param params: HTTP request parameters for GET.
    :param json_payload: JSON body for POST or PUT.
    :param retries: Maximum number of retries.
    :param my_logger: Logger instance.
    :param simplified_circuit_breaker: Circuit breaker instance.
    :return: Dictionary with keys:
        - 'success': bool - True if request succeeded, False otherwise
        - 'result': Any - Response data if success=True, None if success=False
        - 'exception': str - Error message if success=False, not present if success=True
    """
    asyncio.current_task().set_name(f"fetch_with_retries_method_{method}")
    retry_count = 0
    retry_delay = INITIAL_RETRY_DELAY

    last_exception = None
    last_error_type = None
    request_attempts = []  # Track all attempts for debugging
    connector_logger = ConnectorLogger(my_logger)

    # Enhanced logging for debugging
    my_logger.debug(f"Starting fetch_with_retries: {method} {url} (max_retries={retries})")

    while retry_count <= retries:
        attempt_info = {
            "attempt": retry_count + 1,
            "max_retries": retries,
            "url": url,
            "method": method
        }
        my_logger.debug(f"Attempt {attempt_info['attempt']}/{retries + 1}: {method} {url}")

        # Check circuit breaker before making request
        connector_logger.log_connector_attrs(name="fetch_with_retries", session=session)

        # if not await global_circuit_breaker.can_execute():
        if not await simplified_circuit_breaker.can_execute():
            my_logger.warning("Circuit breaker is OPEN or rate limited. Waiting for recovery...")
            status = await simplified_circuit_breaker.get_status()
            backoffs = status["backoffs"]
            current_time = status["current_time"]
            # Calculate wait times for all active backoffs
            wait_times = [
                until_time - current_time
                for until_time in [
                    backoffs["rate_limit_until"],
                    backoffs["connection_pool_until"],
                    backoffs["network_until"],
                    backoffs["global_warning_until"]
                ]
                if until_time > current_time
            ]
            if wait_times:
                wait_time = max(wait_times)
                my_logger.warning(f"Circuit breaker blocked. Waiting {wait_time + 5:.1f}s...")
                await simplified_circuit_breaker.wait_for_clearance(timeout=wait_time + 5.0)

            # Check again after waiting
            if not await simplified_circuit_breaker.can_execute():
                circuit_error = "Circuit breaker OPEN - deferred"
                my_logger.warning(f"Circuit breaker still OPEN after waiting. Request will be deferred: {url}")
                attempt_info["result"] = "circuit_breaker_deferred"
                attempt_info["error"] = circuit_error
                request_attempts.append(attempt_info)
                return {"success": False, "exception": circuit_error, "result": None, "deferred": True}

        # await global_circuit_breaker.enter_request()
        await simplified_circuit_breaker.enter_request()
        request_successful = False
        need_retry = False
        current_attempt_error = None

        try:
            async with session.request(
                    method=method,
                    url=url,
                    params=params,
                    json=json_payload,
            ) as response:
                my_logger.debug(f"status: {response.status}, url: {response.url}")
                if response.status in (200, 201, 204):
                    # await global_circuit_breaker.record_success()
                    await simplified_circuit_breaker.record_success()
                    request_successful = True

                    # Monitor rate limit headers for proactive management
                    rate_limit_remaining = response.headers.get("X-RateLimit-Remaining")
                    rate_limit_limit = response.headers.get("X-RateLimit-Limit")
                    near_limit = response.headers.get("X-RateLimit-NearLimit") == "true"

                    if rate_limit_remaining and rate_limit_limit:
                        remaining_pct = (int(rate_limit_remaining) / int(rate_limit_limit)) * 100
                        my_logger.debug(f"Rate limit status: {rate_limit_remaining}/{rate_limit_limit} ({remaining_pct:.1f}% remaining)")

                    # Success handling
                    attempt_info["result"] = "success"
                    attempt_info["status"] = response.status

                    if response.status == 204:
                        my_logger.info(f"Request successful (204), no content to return. Attempt {retry_count + 1}")
                        attempt_info["content_type"] = "no_content"
                        return {
                            "success": True, "result": None,
                            "metadata": {
                                "status": response.status, "url": response.url,
                                "headers": dict(response.headers),
                                "params": params, "json_payload": json_payload,
                                "attempts": request_attempts + [attempt_info]
                            }
                        }
                    else:
                        if near_limit:
                            # Coordinate global rate limit warning across all threads
                            warning_duration = max(2.0, 2 ** retry_count)  # At least 2 seconds
                            my_logger.warning(f"Warning: Less than 20% of the rate limit budget remains.")
                            my_logger.warning(f"Activating global rate limit warning for {warning_duration} seconds.")
                            await simplified_circuit_breaker.record_rate_limit_warning(warning_duration)
                            await simplified_circuit_breaker.wait_for_clearance(timeout=warning_duration)

                        my_logger.debug(f"Request successful ({response.status}). Attempt {retry_count + 1}")
                        attempt_info["content_type"] = "json"
                        return {
                            "success": True, "result": await response.json(),
                            "metadata": {
                                "status": response.status, "url": response.url,
                                "headers": dict(response.headers),
                                "params": params, "json_payload": json_payload,
                                "attempts": request_attempts + [attempt_info]
                            }
                        }
                elif response.status == 400:
                    bad_request_error = f"Bad request (400)"
                    my_logger.error(f"Attempt {retry_count + 1}: {bad_request_error}. url = {response.url}")
                    attempt_info["result"] = "bad_request"
                    attempt_info["status"] = response.status
                    attempt_info["error"] = bad_request_error
                    return {
                        "success": False, "exception": bad_request_error,
                        "metadata": {
                            "status": response.status, "url": response.url,
                            "headers": dict(response.headers),
                            "params": params, "json_payload": json_payload,
                            "attempts": request_attempts + [attempt_info]
                        }
                    }

                elif response.status == 429 or "Retry-After" in response.headers:
                    # Rate limit - use enhanced error handling
                    retry_delay = await calculate_retry_delay(response, retry_delay)
                    rate_limit_tracker.record_rate_limit(url, retry_delay)

                    # Create rate limit exception for proper classification
                    rate_limit_error = Exception(f"Rate limit (429) - Retry-After: {retry_delay}ms")
                    current_attempt_error = f"Rate limit (429) - Retry-After: {retry_delay}ms"
                    last_exception = current_attempt_error
                    last_error_type = "RateLimit"

                    await simplified_circuit_breaker.record_error(rate_limit_error, retry_delay)
                    need_retry = True

                    my_logger.warning(
                        f"Attempt {retry_count + 1}: Rate limited. Waiting for coordinated recovery. "
                        f"Retry delay: {retry_delay / 1000:.2f}s, "
                        f"Consecutive rate limits: {rate_limit_tracker.consecutive_rate_limits}"
                    )

                    # Wait for rate limit specific recovery
                    await simplified_circuit_breaker.wait_for_clearance(timeout=retry_delay / 1000)

                elif response.status == 503:
                    # Service unavailable - service error
                    retry_delay = await calculate_retry_delay(response, retry_delay)
                    service_error = Exception(f"Service unavailable (503)")
                    current_attempt_error = f"Service unavailable (503)"
                    last_exception = current_attempt_error
                    last_error_type = "ServiceUnavailable"

                    await simplified_circuit_breaker.record_error(service_error)
                    need_retry = True
                    my_logger.warning(f"Attempt {retry_count + 1}: Service unavailable (503). Will retry after backoff.")

                else:
                    # Other HTTP errors - classify and handle appropriately
                    http_error = Exception(f"HTTP {response.status}")
                    http_error_msg = f"HTTP {response.status}"
                    await simplified_circuit_breaker.record_error(http_error)
                    my_logger.error(f"Attempt {retry_count + 1}: Request failed with status {response.status}. url = {response.url}")

                    attempt_info["result"] = "http_error"
                    attempt_info["status"] = response.status
                    attempt_info["error"] = http_error_msg
                    return {
                        "success": False, "exception": http_error_msg,
                        "metadata": {
                            "status": response.status, "url": response.url,
                            "headers": dict(response.headers),
                            "params": params, "json_payload": json_payload,
                            "attempts": request_attempts + [attempt_info]
                        }
                    }
                # response.raise_for_status()

        except aiohttp.ClientResponseError as e:
            current_attempt_error = f"HTTP error {e.status}: {e.message}"
            my_logger.info(f"Attempt {retry_count + 1}: {current_attempt_error}")
            last_exception = current_attempt_error
            last_error_type = "ClientResponseError"

            # Use enhanced error handling with proper classification
            await simplified_circuit_breaker.record_error(e)
            need_retry = True  # Allow retry for retriable HTTP errors

            error_type = simplified_circuit_breaker.classify_error(e)
            if error_type == ErrorType.RATE_LIMIT:
                await simplified_circuit_breaker.wait_for_clearance()

        except aiohttp.client_exceptions.ConnectionTimeoutError as e:
            current_attempt_error = f"Connection timeout: {str(e)}"
            my_logger.warning(f"Attempt {retry_count + 1}: {current_attempt_error}. Will retry with network error handling. {url}")
            last_exception = current_attempt_error
            last_error_type = "ConnectionTimeoutError"
            await simplified_circuit_breaker.record_error(e)
            need_retry = True
            await simplified_circuit_breaker.wait_for_clearance()

        except aiohttp.ClientSSLError as e:
            current_attempt_error = f"SSL error: {str(e)}"
            my_logger.warning(f"Attempt {retry_count + 1}: {current_attempt_error}. Will retry with appropriate handling. {url}")
            last_exception = current_attempt_error
            last_error_type = "ClientSSLError"

            await simplified_circuit_breaker.record_error(e)
            need_retry = True

            error_type = simplified_circuit_breaker.classify_error(e)
            await simplified_circuit_breaker.wait_for_clearance()

        except aiohttp.ClientConnectorError as e:
            host, port, ssl = e.host, e.port, e.ssl
            os_error = e.os_error
            current_attempt_error = f"Connector error {ssl} {host}:{port}: {os_error}"
            my_logger.warning(f"Attempt {retry_count + 1}: {current_attempt_error}. Will retry with backoff. {url}")
            last_exception = current_attempt_error
            last_error_type = "ClientConnectorError"

            await simplified_circuit_breaker.record_error(e)
            need_retry = True
            await simplified_circuit_breaker.wait_for_clearance()

        except aiohttp.ClientOSError as e:
            current_attempt_error = f"Client OSError: {str(e)}"
            my_logger.warning(f"Attempt {retry_count + 1}: {current_attempt_error}. Will retry with appropriate handling. {url}")
            last_exception = current_attempt_error
            last_error_type = "ClientOSError"
            await simplified_circuit_breaker.record_error(e)
            need_retry = True
            await simplified_circuit_breaker.wait_for_clearance()

        except aiohttp.client_exceptions.ClientConnectionResetError as e:
            current_attempt_error = f"Connection reset: {str(e)}"
            my_logger.warning(f"Attempt {retry_count + 1}: {current_attempt_error}. Will retry with network error handling. {url}")
            last_exception = current_attempt_error
            last_error_type = "ClientConnectionResetError"

            await simplified_circuit_breaker.record_error(e)
            need_retry = True

        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            current_attempt_error = f"Client/timeout error: {str(e)}"
            my_logger.warning(f"Attempt {retry_count + 1}: {current_attempt_error}. Will retry with appropriate handling. {url}")
            last_exception = current_attempt_error
            last_error_type = "ClientError/TimeoutError"

            await simplified_circuit_breaker.record_error(e)
            need_retry = True

            error_type = simplified_circuit_breaker.classify_error(e)
            await simplified_circuit_breaker.wait_for_clearance()

        except Exception as e:
            current_attempt_error = f"Unexpected error: {str(e)}"
            my_logger.error(f"Attempt {retry_count + 1}: {current_attempt_error}")
            last_exception = current_attempt_error
            last_error_type = "UnexpectedError"

            # For unexpected errors, don't retry and return failure immediately
            attempt_info["result"] = "unexpected_error"
            attempt_info["error"] = current_attempt_error
            attempt_info["error_type"] = last_error_type
            request_attempts.append(attempt_info)

            my_logger.error(f"Request attempts summary: {request_attempts}")
            return {
                "success": False, "exception": current_attempt_error,
                "metadata": {
                    "status": None, "url": url,
                    "headers": None,
                    "params": params, "json_payload": json_payload,
                    "attempts": request_attempts
                }
            }
        finally:
            await simplified_circuit_breaker.exit_request()
            await asyncio.sleep(0.250)

            # Record attempt information for debugging
            attempt_info["error"] = current_attempt_error
            attempt_info["error_type"] = last_error_type
            attempt_info["need_retry"] = need_retry
            attempt_info["request_successful"] = request_successful
            request_attempts.append(attempt_info.copy())

        # If request was successful, return immediately (no retry needed)
        if request_successful:
            my_logger.debug(f"Request successful on attempt {retry_count + 1}")
            break

        # Handle retry logic with enhanced error-specific retry counts
        if need_retry:
            # Get circuit status to determine effective retry limit
            circuit_status = await simplified_circuit_breaker.get_status()
            effective_retries = retries

            # For network errors, use doubled retry count
            if last_exception and any(keyword in last_exception.lower() for keyword in [
                "connection timeout", "connection reset", "network", "dns", "ssl"
            ]):
                effective_retries = int(math.ceil(retries * circuit_status.get("network_retry_multiplier", 2)))
                delay = max(2.0, 2 ** retry_count)
                my_logger.debug(f"Network error detected. Using enhanced retry count: {effective_retries}")
            else:
                effective_retries = retries
                delay = 2.0  # fixed small delay for non-network errors
                my_logger.debug(f"Non-network error. Retries allowed: {effective_retries}, next delay={delay}s")

            if retry_count < effective_retries:
                retry_count += 1
                my_logger.debug(f"Will retry: attempt {retry_count + 1} of {effective_retries + 1} with delay={delay}s")
                await asyncio.sleep(delay)
            else:
                # Exhausted retries
                my_logger.warning(f"Exceeded maximum retries ({effective_retries}) for this error type.")
                break
        else:
            # No retry needed but request failed - this shouldn't happen
            my_logger.warning(f"Request failed but need_retry=False. This indicates a logic error.")
            break

        # Handle retry delay for non-coordinated retries
        if retry_count > effective_retries:
            my_logger.error(f"Exceeded maximum retries (retry_count={retry_count} and effective_retries = {effective_retries}).")
            break

        # Add minimal jitter for non-coordinated retries
        # if global_circuit_breaker.state == CircuitState.CLOSED:
        #     jitter = random.uniform(*JITTER_MULTIPLIER_RANGE)
        #     minimal_delay = (retry_delay + jitter) / 1000
        #     if minimal_delay > 0.1:  # Only sleep if delay is meaningful
        #         await asyncio.sleep(minimal_delay)

    # If we reach here, all retries have been exhausted
    # Return failure response with the last exception encountered

    # Enhanced debugging information
    # my_logger.error(f"All retries exhausted for {method} {url}")
    # my_logger.error(f"Total attempts made: {len(request_attempts)}")
    # my_logger.error(f"Request attempts summary: {request_attempts}")

    if last_exception:
        my_logger.error(f"Last error: {last_exception} (type: {last_error_type})")

        # Classify the error to determine if shutdown is needed
        # try:
        #     # Create exception object for classification
        #     if "rate limit" in last_exception.lower() or "429" in last_exception:
        #         error_for_classification = Exception(f"Rate limit: {last_exception}")
        #     elif "connection" in last_exception.lower():
        #         error_for_classification = Exception(f"Connection error: {last_exception}")
        #     else:
        #         error_for_classification = Exception(last_exception)
        #
        # except Exception as e:
        #     my_logger.error(f"Error during error classification: {e}")

        return {
            "success": False, "exception": last_exception, "deferred": True,
            "metadata": {
                "status": None, "url": url,
                "headers": None,
                "params": params, "json_payload": json_payload,
                "attempts": request_attempts,
                "last_error_type": last_error_type
            }
        }
    else:
        # This should never happen now with our improved error tracking
        error_msg = "All retries exhausted with no specific error recorded"
        my_logger.error(f"{error_msg}. This indicates a logic error in fetch_with_retries.")
        my_logger.error(f"Debug info - retry_count: {retry_count}, retries: {retries}")
        my_logger.error(f"Debug info - request_attempts: {request_attempts}")

        # Log circuit breaker status for debugging
        try:
            circuit_status = await simplified_circuit_breaker.get_status()
            my_logger.error(f"Circuit breaker status: {circuit_status}")
        except Exception as e:
            my_logger.error(f"Failed to get circuit breaker status: {e}")

        return {
            "success": False, "exception": error_msg,
            "metadata": {
                "status": None, "url": url,
                "headers": None,
                "params": params, "json_payload": json_payload,
                "attempts": request_attempts,
                "debug_info": {
                    "retry_count": retry_count,
                    "max_retries": retries,
                    "last_error_type": last_error_type
                }
            }
        }


async def fetch_with_retries_post(
        session: aiohttp.ClientSession,
        url: str,
        json_payload: Dict[str, Any]
) -> Dict[str, Any]:
    """
    POST wrapper for fetch_with_retries.

    :return: Dictionary with keys 'success', 'result', and 'exception' (if failed)
    """
    asyncio.current_task().set_name("fetch_with_retries_post")
    return await fetch_with_retries(
        session=session,
        method="POST",
        url=url,
        json_payload=json_payload,
    )


async def fetch_with_retries_get(
        session: aiohttp.ClientSession,
        url: str,
        params: Union[Dict[str, Any], None] = None,
) -> Dict[str, Any]:
    """
    GET wrapper for fetch_with_retries.

    :return: Dictionary with keys 'success', 'result', and 'exception' (if failed)
    """
    asyncio.current_task().set_name("fetch_with_retries_get")
    return await fetch_with_retries(
        session=session,
        method="GET",
        url=url,
        params=params
    )


async def calculate_retry_delay(
        response: aiohttp.ClientResponse, last_retry_delay: float,
        my_logger: Logger = Provide[LoggerContainer.logger]
) -> float:
    """
    Calculate the delay for the next retry attempt.

    :param response: The HTTP response object.
    :param last_retry_delay: The last retry delay in milliseconds.
    :param my_logger: logger instance Injected from container
    :return: The new retry delay in milliseconds.
    """
    retry_delay = -1

    # Check for the Retry-After header
    if "Retry-After" in response.headers:
        retry_delay = int(response.headers["Retry-After"]) * 1000  # Convert to milliseconds

    # Check for the X-RateLimit-Reset header
    elif "X-RateLimit-Reset" in response.headers:
        reset_time = parser.parse(response.headers["X-RateLimit-Reset"])
        current_time = datetime.now(timezone.utc)
        wait_time = (reset_time - current_time).total_seconds() * 1000  # Convert to milliseconds
        retry_delay = max(wait_time, last_retry_delay)

    # If no headers are present but 429 status is received, double the last delay
    elif response.status == 429:
        retry_delay = min(2 * last_retry_delay, MAX_RETRY_DELAY)

    elif response.status in (503, 500) and "Retry-After" in response.headers:
        # Handle transient 5XX errors with Retry-After header
        retry_delay = int(response.headers["Retry-After"]) * 1000  # Convert to milliseconds

    # Apply jitter
    if retry_delay > 0:
        jitter = random.uniform(*JITTER_MULTIPLIER_RANGE)
        retry_delay += int(retry_delay * jitter)

    # Log and handle X-RateLimit-NearLimit
    if response.headers.get("X-RateLimit-NearLimit") == "true":
        retry_delay = max(retry_delay, 5000)  # Example delay of 5 seconds
        my_logger.warning(f"Less than 20% of the budget remains. sleeping for {retry_delay / 1000} seconds.")
        # Introduce a delay when near limit is reached
        await asyncio.sleep(retry_delay / 1000)

    return retry_delay


logger_container = LoggerContainer()
logger_container.wire(modules=["dags.data_pipeline.jira.api_client"])
circuit_breaker_container = CircuitBreakerContainer()
circuit_breaker_container.wire(modules=["dags.data_pipeline.jira.api_client"])

# Lazy import to avoid circular import
# def get_application_container():
#     """Lazy import of EnhancedApplicationContainer to avoid circular imports."""
#     from dags.data_pipeline.containers import EnhancedApplicationContainer
#     return EnhancedApplicationContainer()

# Initialize application container lazily when needed
# application_container = get_application_container()
# application_container.wire(modules=["dags.data_pipeline.jira.api_client"])
# circuit_breaker_container = CircuitBreakerContainer()
# circuit_breaker_container.wire(modules=["dags.data_pipeline.jira.api_client"])
